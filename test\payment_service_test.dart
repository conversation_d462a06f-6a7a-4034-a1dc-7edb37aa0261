import 'package:flutter_test/flutter_test.dart';
import 'package:pay_moyasar/services/payment_service.dart';

void main() {
  group('PaymentService Tests', () {
    test('parseUrlParameters should correctly parse URL parameters', () {
      const testUrl = 'https://tak.runasp.net/moyasar/?id=5940a619-100a-41b5-b67c-3e7651116a89&status=paid&message=APPROVED&invoice_id=36edb481-0a35-4708-9c4c-8f2661493f8c';
      
      final params = PaymentService.parseUrlParameters(testUrl);
      
      expect(params['id'], '5940a619-100a-41b5-b67c-3e7651116a89');
      expect(params['status'], 'paid');
      expect(params['message'], 'APPROVED');
      expect(params['invoice_id'], '36edb481-0a35-4708-9c4c-8f2661493f8c');
    });

    test('parseUrlParameters should handle URL without parameters', () {
      const testUrl = 'https://tak.runasp.net/moyasar/';
      
      final params = PaymentService.parseUrlParameters(testUrl);
      
      expect(params.isEmpty, true);
    });

    test('parseUrlParameters should handle malformed URL', () {
      const testUrl = 'not-a-valid-url';
      
      expect(() => PaymentService.parseUrlParameters(testUrl), throwsFormatException);
    });
  });
}

import 'package:flutter/material.dart';
import '../services/payment_service.dart';
import '../models/payment_models.dart';

class PaymentCallbackScreen extends StatefulWidget {
  final String callbackUrl;
  final String reservationGuid;

  const PaymentCallbackScreen({
    super.key,
    required this.callbackUrl,
    required this.reservationGuid,
  });

  @override
  State<PaymentCallbackScreen> createState() => _PaymentCallbackScreenState();
}

class _PaymentCallbackScreenState extends State<PaymentCallbackScreen> {
  bool _isLoading = true;
  bool _paymentSuccess = false;
  String _message = '';
  ReservationData? _reservationData;

  @override
  void initState() {
    super.initState();
    _checkPaymentStatus();
  }

  Future<void> _checkPaymentStatus() async {
    try {
      // Parse URL parameters
      final urlParams = PaymentService.parseUrlParameters(widget.callbackUrl);
      final status = urlParams['status'];
      
      if (status == 'paid') {
        // Payment was successful, get reservation details
        final response = await PaymentService.getReservationById(
          reservationGuid: widget.reservationGuid,
        );
        
        if (response.success && response.data != null) {
          setState(() {
            _paymentSuccess = true;
            _message = 'Payment completed successfully!';
            _reservationData = response.data;
            _isLoading = false;
          });
        } else {
          setState(() {
            _paymentSuccess = false;
            _message = response.message;
            _isLoading = false;
          });
        }
      } else {
        setState(() {
          _paymentSuccess = false;
          _message = 'Payment was not successful. Status: ${status ?? 'unknown'}';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _paymentSuccess = false;
        _message = 'Error checking payment status: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment Status'),
        backgroundColor: _paymentSuccess ? Colors.green : Colors.red,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: _isLoading
            ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Checking payment status...'),
                  ],
                ),
              )
            : _buildResultContent(),
      ),
    );
  }

  Widget _buildResultContent() {
    if (_paymentSuccess && _reservationData != null) {
      // Success view with ticket details
      return Column(
        children: [
          // Success header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24.0),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.green.shade400, Colors.green.shade600],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Column(
              children: [
                const Icon(
                  Icons.check_circle,
                  color: Colors.white,
                  size: 80,
                ),
                const SizedBox(height: 16),
                const Text(
                  '🎉 تم الدفع بنجاح!',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  _message,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          // Ticket details
          Expanded(
            child: _buildReservationDetails(),
          ),

          // Back button
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: ElevatedButton(
              onPressed: () {
                Navigator.of(context).popUntil((route) => route.isFirst);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
                minimumSize: const Size(double.infinity, 50),
              ),
              child: const Text(
                '🏠 العودة للرئيسية',
                style: TextStyle(fontSize: 16),
              ),
            ),
          ),
        ],
      );
    } else {
      // Error view
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error,
              color: Colors.red,
              size: 100,
            ),
            const SizedBox(height: 24),
            Text(
              'فشل في الدفع',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              _message,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 48),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).popUntil((route) => route.isFirst);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
              ),
              child: const Text(
                'العودة للرئيسية',
                style: TextStyle(fontSize: 16),
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildReservationDetails() {
    final reservation = _reservationData!;
    final payment = reservation.payments.isNotEmpty ? reservation.payments.first : null;

    return SingleChildScrollView(
      child: Column(
        children: [
          // Reservation Details Card
          Card(
            margin: const EdgeInsets.all(8.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '🎫 تفاصيل الحجز',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildDetailRow('رقم الحجز', reservation.bookingId),
                  _buildDetailRow('معرف الحجز', reservation.reservationGUID),
                  _buildDetailRow('حالة الحجز', reservation.isCaptured ? 'مؤكد ✅' : 'في الانتظار ⏳'),
                  _buildDetailRow('معرف المستخدم', reservation.userId.toString()),
                ],
              ),
            ),
          ),

          // Payment Details Card
          if (payment != null)
            Card(
              margin: const EdgeInsets.all(8.0),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '💳 تفاصيل الدفع',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildDetailRow('المبلغ المدفوع', '${payment.amount} ${payment.currency}'),
                    _buildDetailRow('حالة الدفع', payment.status.toUpperCase() == 'PAID' ? 'مدفوع ✅' : payment.status),
                    _buildDetailRow('رسالة الدفع', payment.message),
                    _buildDetailRow('معرف الفاتورة', payment.invoiceId),
                    _buildDetailRow('معرف الدفع', payment.paymentId),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.black54,
                fontWeight: FontWeight.w400,
              ),
              textAlign: TextAlign.left,
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import '../services/payment_service.dart';
import '../models/payment_models.dart';

class PaymentCallbackScreen extends StatefulWidget {
  final String callbackUrl;
  final String reservationGuid;

  const PaymentCallbackScreen({
    super.key,
    required this.callbackUrl,
    required this.reservationGuid,
  });

  @override
  State<PaymentCallbackScreen> createState() => _PaymentCallbackScreenState();
}

class _PaymentCallbackScreenState extends State<PaymentCallbackScreen> {
  bool _isLoading = true;
  bool _paymentSuccess = false;
  String _message = '';
  ReservationData? _reservationData;

  @override
  void initState() {
    super.initState();
    _checkPaymentStatus();
  }

  Future<void> _checkPaymentStatus() async {
    try {
      // Parse URL parameters
      final urlParams = PaymentService.parseUrlParameters(widget.callbackUrl);
      final status = urlParams['status'];
      
      if (status == 'paid') {
        // Payment was successful, get reservation details
        final response = await PaymentService.getReservationById(
          reservationGuid: widget.reservationGuid,
        );
        
        if (response.success && response.data != null) {
          setState(() {
            _paymentSuccess = true;
            _message = 'Payment completed successfully!';
            _reservationData = response.data;
            _isLoading = false;
          });
        } else {
          setState(() {
            _paymentSuccess = false;
            _message = response.message;
            _isLoading = false;
          });
        }
      } else {
        setState(() {
          _paymentSuccess = false;
          _message = 'Payment was not successful. Status: ${status ?? 'unknown'}';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _paymentSuccess = false;
        _message = 'Error checking payment status: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment Status'),
        backgroundColor: _paymentSuccess ? Colors.green : Colors.red,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: _isLoading
            ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Checking payment status...'),
                  ],
                ),
              )
            : _buildResultContent(),
      ),
    );
  }

  Widget _buildResultContent() {
    if (_paymentSuccess && _reservationData != null) {
      // Success view with ticket details
      return Column(
        children: [
          // Success header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24.0),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.green.shade400, Colors.green.shade600],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Column(
              children: [
                const Icon(
                  Icons.check_circle,
                  color: Colors.white,
                  size: 80,
                ),
                const SizedBox(height: 16),
                const Text(
                  '🎉 تم الدفع بنجاح!',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  _message,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          // Ticket details
          Expanded(
            child: _buildReservationDetails(),
          ),

          // Back button
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: ElevatedButton(
              onPressed: () {
                Navigator.of(context).popUntil((route) => route.isFirst);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
                minimumSize: const Size(double.infinity, 50),
              ),
              child: const Text(
                '🏠 العودة للرئيسية',
                style: TextStyle(fontSize: 16),
              ),
            ),
          ),
        ],
      );
    } else {
      // Error view
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error,
              color: Colors.red,
              size: 100,
            ),
            const SizedBox(height: 24),
            Text(
              'فشل في الدفع',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              _message,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 48),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).popUntil((route) => route.isFirst);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
              ),
              child: const Text(
                'العودة للرئيسية',
                style: TextStyle(fontSize: 16),
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildReservationDetails() {
    final reservation = _reservationData!;
    final payment = reservation.payments.isNotEmpty ? reservation.payments.first : null;
    final flight = reservation.flights.isNotEmpty ? reservation.flights.first : null;
    final traveler = reservation.travelers.isNotEmpty ? reservation.travelers.first : null;

    return SingleChildScrollView(
      child: Column(
        children: [
          // Reservation Details Card
          Card(
            margin: const EdgeInsets.all(8.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '🎫 تفاصيل الحجز',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildDetailRow('رقم الحجز', reservation.bookingId),
                  _buildDetailRow('معرف الحجز', reservation.reservationGUID),
                  _buildDetailRow('حالة الحجز', reservation.isCaptured ? 'مؤكد ✅' : 'في الانتظار ⏳'),
                  _buildDetailRow('معرف المستخدم', reservation.userId.toString()),
                  if (reservation.queuingOfficeId.isNotEmpty)
                    _buildDetailRow('مكتب الحجز', reservation.queuingOfficeId),
                ],
              ),
            ),
          ),

          // Flight Details Card
          if (flight != null)
            Card(
              margin: const EdgeInsets.all(8.0),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '✈️ تفاصيل الرحلة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildDetailRow('رقم المرجع', flight.reference),
                    _buildDetailRow('مصدر الحجز', flight.source),
                    _buildDetailRow('آخر موعد للإصدار', _formatDate(flight.lastTicketingDate)),
                    _buildDetailRow('تاريخ الإنشاء', _formatDate(flight.creationDate)),
                    _buildDetailRow('العملة', flight.currency),
                    _buildDetailRow('السعر الأساسي', '${flight.base} ${flight.currency}'),
                    _buildDetailRow('المجموع', '${flight.total} ${flight.currency}'),
                    _buildDetailRow('المبلغ النهائي', '${flight.final_} ${flight.currency}'),

                    // Flight segments
                    if (flight.flightDetails.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      const Text(
                        '🛫 تفاصيل الرحلة',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                      const SizedBox(height: 8),
                      ...flight.flightDetails.map((detail) => _buildFlightSegment(detail)),
                    ],
                  ],
                ),
              ),
            ),

          // Traveler Details Card
          if (traveler != null)
            Card(
              margin: const EdgeInsets.all(8.0),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '👤 بيانات المسافر',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.purple,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildDetailRow('الاسم الأول', traveler.firstName),
                    _buildDetailRow('الاسم الأخير', traveler.lastName),
                    _buildDetailRow('الجنس', traveler.gender == 'MALE' ? 'ذكر' : 'أنثى'),
                    _buildDetailRow('تاريخ الميلاد', _formatDate(traveler.dateOfBirth)),
                    _buildDetailRow('رقم الوثيقة', traveler.documentNumber),
                    _buildDetailRow('تاريخ انتهاء الوثيقة', _formatDate(traveler.documentExpiryDate)),
                    _buildDetailRow('الجنسية', traveler.documentNationality),
                    _buildDetailRow('البريد الإلكتروني', traveler.contactEmailAddress),
                    _buildDetailRow('رقم الهاتف', traveler.contactPhoneNumber),
                  ],
                ),
              ),
            ),

          // Payment Details Card
          if (payment != null)
            Card(
              margin: const EdgeInsets.all(8.0),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '💳 تفاصيل الدفع',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildDetailRow('المبلغ المدفوع', '${payment.amount} ${payment.currency}'),
                    _buildDetailRow('الرسوم', '${payment.fee} ${payment.currency}'),
                    _buildDetailRow('حالة الدفع', payment.status.toUpperCase() == 'PAID' ? 'مدفوع ✅' : payment.status),
                    _buildDetailRow('رسالة الدفع', payment.message),
                    _buildDetailRow('شركة البطاقة', payment.company.toUpperCase()),
                    _buildDetailRow('اسم حامل البطاقة', payment.name),
                    _buildDetailRow('رقم البطاقة', payment.number),
                    _buildDetailRow('معرف الفاتورة', payment.invoiceId),
                    _buildDetailRow('معرف الدفع', payment.paymentId),
                    _buildDetailRow('تاريخ الإنشاء', _formatDateTime(payment.createdAt)),
                    _buildDetailRow('تاريخ التحديث', _formatDateTime(payment.updatedAt)),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.black54,
                fontWeight: FontWeight.w400,
              ),
              textAlign: TextAlign.left,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFlightSegment(FlightDetail detail) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '${detail.carrierCode} ${detail.number}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  detail.aircraftCode,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Flight route
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      detail.departureIataCode,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      _formatDateTime(detail.departureAt),
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),
                    if (detail.departureTerminal.isNotEmpty)
                      Text(
                        'Terminal ${detail.departureTerminal}',
                        style: const TextStyle(
                          fontSize: 10,
                          color: Colors.grey,
                        ),
                      ),
                  ],
                ),
              ),

              Column(
                children: [
                  const Icon(Icons.flight_takeoff, color: Colors.blue),
                  Text(
                    _formatDuration(detail.duration),
                    style: const TextStyle(
                      fontSize: 10,
                      color: Colors.grey,
                    ),
                  ),
                  if (detail.numberOfStops > 0)
                    Text(
                      '${detail.numberOfStops} توقف',
                      style: const TextStyle(
                        fontSize: 10,
                        color: Colors.orange,
                      ),
                    )
                  else
                    const Text(
                      'مباشر',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.green,
                      ),
                    ),
                ],
              ),

              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      detail.arrivalIataCode,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      _formatDateTime(detail.arrivalAt),
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatDate(String dateStr) {
    if (dateStr.isEmpty) return '';
    try {
      final date = DateTime.parse(dateStr);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateStr;
    }
  }

  String _formatDateTime(String dateStr) {
    if (dateStr.isEmpty) return '';
    try {
      final date = DateTime.parse(dateStr);
      return '${date.day}/${date.month}/${date.year} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return dateStr;
    }
  }

  String _formatDuration(String duration) {
    if (duration.isEmpty) return '';
    // Convert PT1H5M to 1h 5m
    final regex = RegExp(r'PT(?:(\d+)H)?(?:(\d+)M)?');
    final match = regex.firstMatch(duration);
    if (match != null) {
      final hours = match.group(1);
      final minutes = match.group(2);
      String result = '';
      if (hours != null) result += '${hours}س ';
      if (minutes != null) result += '${minutes}د';
      return result.trim();
    }
    return duration;
  }
}

import 'package:flutter/material.dart';
import '../services/payment_service.dart';
import '../models/payment_models.dart';

class PaymentCallbackScreen extends StatefulWidget {
  final String callbackUrl;
  final String reservationGuid;

  const PaymentCallbackScreen({
    super.key,
    required this.callbackUrl,
    required this.reservationGuid,
  });

  @override
  State<PaymentCallbackScreen> createState() => _PaymentCallbackScreenState();
}

class _PaymentCallbackScreenState extends State<PaymentCallbackScreen> {
  bool _isLoading = true;
  bool _paymentSuccess = false;
  String _message = '';
  ReservationData? _reservationData;

  @override
  void initState() {
    super.initState();
    _checkPaymentStatus();
  }

  Future<void> _checkPaymentStatus() async {
    try {
      // Parse URL parameters
      final urlParams = PaymentService.parseUrlParameters(widget.callbackUrl);
      final status = urlParams['status'];
      
      if (status == 'paid') {
        // Payment was successful, get reservation details
        final response = await PaymentService.getReservationById(
          reservationGuid: widget.reservationGuid,
        );
        
        if (response.success && response.data != null) {
          setState(() {
            _paymentSuccess = true;
            _message = 'Payment completed successfully!';
            _reservationData = response.data;
            _isLoading = false;
          });
        } else {
          setState(() {
            _paymentSuccess = false;
            _message = response.message;
            _isLoading = false;
          });
        }
      } else {
        setState(() {
          _paymentSuccess = false;
          _message = 'Payment was not successful. Status: ${status ?? 'unknown'}';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _paymentSuccess = false;
        _message = 'Error checking payment status: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment Status'),
        backgroundColor: _paymentSuccess ? Colors.green : Colors.red,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: _isLoading
            ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Checking payment status...'),
                  ],
                ),
              )
            : _buildResultContent(),
      ),
    );
  }

  Widget _buildResultContent() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _paymentSuccess ? Icons.check_circle : Icons.error,
            color: _paymentSuccess ? Colors.green : Colors.red,
            size: 100,
          ),
          const SizedBox(height: 24),
          Text(
            _paymentSuccess ? 'Payment Successful!' : 'Payment Failed',
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: _paymentSuccess ? Colors.green : Colors.red,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            _message,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
          if (_reservationData != null) ...[
            const SizedBox(height: 24),
            _buildReservationDetails(),
          ],
          const SizedBox(height: 48),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).popUntil((route) => route.isFirst);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: _paymentSuccess ? Colors.green : Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: 32,
                vertical: 16,
              ),
            ),
            child: const Text(
              'Back to Home',
              style: TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReservationDetails() {
    final reservation = _reservationData!;
    final payment = reservation.payments.isNotEmpty ? reservation.payments.first : null;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Reservation Details',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildDetailRow('Booking ID', reservation.bookingId),
            _buildDetailRow('Reservation GUID', reservation.reservationGUID),
            _buildDetailRow('Status', reservation.isCaptured ? 'Captured' : 'Pending'),
            if (payment != null) ...[
              const SizedBox(height: 8),
              const Text(
                'Payment Details',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              _buildDetailRow('Amount', '${payment.amount} ${payment.currency}'),
              _buildDetailRow('Status', payment.status.toUpperCase()),
              _buildDetailRow('Message', payment.message),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }
}

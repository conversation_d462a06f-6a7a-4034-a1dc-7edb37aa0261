import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/payment_models.dart';

class PaymentService {
  static const String baseUrl = 'https://takeed.runasp.net/api/v1/moyasarv';
  static const String reservationBaseUrl = 'https://takeed.runasp.net/api/v1/reservation';

  static Future<PaymentResponse> createInvoice({
    required String reservationGuid,
    required String deviceToken,
  }) async {
    try {
      final url = Uri.parse('$baseUrl/create-invoice');

      final paymentRequest = PaymentRequest(
        paymentMethod: 'invoice',
        reservationGuid: reservationGuid,
        deviceToken: deviceToken,
      );

      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(paymentRequest.toJson()),
      );

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        return PaymentResponse.fromJson(jsonResponse);
      } else {
        return PaymentResponse(
          statusCode: response.statusCode,
          success: false,
          message: 'Failed to create invoice: ${response.statusCode}',
        );
      }
    } catch (e) {
      return PaymentResponse(
        statusCode: 0,
        success: false,
        message: 'Error: $e',
      );
    }
  }

  static Future<ReservationResponse> getReservationById({
    required String reservationGuid,
  }) async {
    try {
      final url = Uri.parse('$reservationBaseUrl/get-by-id?ReservationGUID=$reservationGuid');

      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        return ReservationResponse.fromJson(jsonResponse);
      } else {
        return ReservationResponse(
          statusCode: response.statusCode,
          success: false,
          message: 'Failed to get reservation: ${response.statusCode}',
        );
      }
    } catch (e) {
      return ReservationResponse(
        statusCode: 0,
        success: false,
        message: 'Error: $e',
      );
    }
  }

  static Map<String, String> parseUrlParameters(String url) {
    final uri = Uri.parse(url);
    return uri.queryParameters;
  }
}

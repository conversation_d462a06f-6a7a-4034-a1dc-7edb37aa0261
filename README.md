# Moyasar Payment App

تطبيق Flutter للدفع باستخدام Moyasar API.

## الوصف

هذا التطبيق يوفر واجهة بسيطة للدفع عبر Moyasar. يقوم التطبيق بإرسال طلب إلى API لإنشاء فاتورة دفع، ثم يفتح رابط الدفع في المتصفح.

## الميزات

- واجهة مستخدم بسيطة لإدخال بيانات الدفع
- إرسال طلب POST إلى Moyasar API
- فتح رابط الدفع في WebView داخل التطبيق
- رصد تلقائي لنجاح الدفع عبر URL redirect (كل 5 ثواني)
- عرض تفاصيل التذكرة والحجز بتصميم جذاب
- معالجة الأخطاء وعرض رسائل الحالة
- صفحة نجاح للدفع
- التحقق من حالة الدفع عبر callback URL
- عرض تفاصيل الحجز والدفع بعد النجاح

## كيفية الاستخدام

### للدفع الجديد:
1. قم بتشغيل التطبيق
2. أدخل Reservation GUID و Device Token
3. اضغط على "Proceed to Payment"
4. سيتم فتح صفحة الدفع في WebView داخل التطبيق
5. أكمل عملية الدفع في الصفحة المعروضة
6. سيتم رصد نجاح الدفع تلقائياً وعرض تفاصيل الحجز

### للتحقق من حالة الدفع:
1. انسخ رابط callback من Moyasar (مثل: https://tak.runasp.net/moyasar/?id=...&status=paid...)
2. الصق الرابط في حقل "Callback URL from Moyasar"
3. تأكد من أن Reservation GUID مُدخل
4. اضغط على "Check Payment Status"
5. سيتم عرض تفاصيل الحجز والدفع إذا كان الدفع ناجحاً

## API المستخدم

### إنشاء فاتورة الدفع
- **Endpoint**: `https://takeed.runasp.net/api/v1/moyasarv/create-invoice`
- **Method**: POST
- **Payload**:
  ```json
  {
    "paymentMethod": "invoice",
    "reservationGuid": "2a89c8c1e4ef4b1f9aa19e2d22c24392",
    "deviceToken": "string"
  }
  ```

### التحقق من حالة الحجز
- **Endpoint**: `https://takeed.runasp.net/api/v1/reservation/get-by-id?ReservationGUID={guid}`
- **Method**: GET
- **Response**: يحتوي على تفاصيل الحجز والدفع

## التشغيل

```bash
flutter pub get
flutter run
```

## المتطلبات

- Flutter SDK
- Dart SDK
- اتصال بالإنترنت للوصول إلى Moyasar API

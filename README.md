# Moyasar Payment App

تطبيق Flutter للدفع باستخدام Moyasar API.

## الوصف

هذا التطبيق يوفر واجهة بسيطة للدفع عبر Moyasar. يقوم التطبيق بإرسال طلب إلى API لإنشاء فاتورة دفع، ثم يفتح رابط الدفع في المتصفح.

## الميزات

- واجهة مستخدم بسيطة لإدخال بيانات الدفع
- إرسال طلب POST إلى Moyasar API
- فتح رابط الدفع في المتصفح الخارجي
- معالجة الأخطاء وعرض رسائل الحالة
- صفحة نجاح للدفع

## كيفية الاستخدام

1. قم بتشغيل التطبيق
2. أدخل Reservation GUID و Device Token
3. اضغط على "Proceed to Payment"
4. سيتم فتح صفحة الدفع في المتصفح
5. بعد إتمام الدفع بنجاح، ستتم إعادة التوجيه إلى tak.runasp.net/moyasar

## API المستخدم

- **Endpoint**: `https://takeed.runasp.net/api/v1/moyasarv/create-invoice`
- **Method**: POST
- **Payload**:
  ```json
  {
    "paymentMethod": "invoice",
    "reservationGuid": "2a89c8c1e4ef4b1f9aa19e2d22c24392",
    "deviceToken": "string"
  }
  ```

## التشغيل

```bash
flutter pub get
flutter run
```

## المتطلبات

- Flutter SDK
- Dart SDK
- اتصال بالإنترنت للوصول إلى Moyasar API

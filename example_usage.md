# مثال على الاستخدام

## سيناريو كامل للدفع

### 1. إنشاء فاتورة دفع جديدة

```
Reservation GUID: 2a89c8c1e4ef4b1f9aa19e2d22c24392
Device Token: string
```

اضغط على "Proceed to Payment" وسيتم إرسال الطلب التالي:

```json
POST https://takeed.runasp.net/api/v1/moyasarv/create-invoice
{
  "paymentMethod": "invoice",
  "reservationGuid": "2a89c8c1e4ef4b1f9aa19e2d22c24392",
  "deviceToken": "string"
}
```

### 2. الاستجابة المتوقعة

```json
{
  "statusCode": 200,
  "success": true,
  "message": "Completed successfully",
  "data": "https://checkout.moyasar.com/invoices/c989a2c2-25a8-4283-bfef-77980340ac0f?lang=en"
}
```

### 3. عملية الدفع في WebView

- سيتم فتح صفحة الدفع داخل التطبيق في WebView
- أكمل عملية الدفع (أدخل بيانات البطاقة، إلخ)
- بعد إتمام الدفع، سيتم إعادة التوجيه تلقائياً إلى رابط مثل:
```
https://tak.runasp.net/moyasar/?id=5940a619-100a-41b5-b67c-3e7651116a89&status=paid&message=APPROVED&invoice_id=36edb481-0a35-4708-9c4c-8f2661493f8c
```
- التطبيق سيرصد هذا التوجيه تلقائياً ويتحقق من حالة الدفع

### 4. التحقق التلقائي من حالة الدفع

عندما يرصد التطبيق redirect إلى `tak.runasp.net/moyasar` مع `status=paid`، سيقوم تلقائياً بـ:

1. إرسال طلب GET للتحقق من تفاصيل الحجز:
```
GET https://takeed.runasp.net/api/v1/reservation/get-by-id?ReservationGUID=2a89c8c1e4ef4b1f9aa19e2d22c24392
```

2. عرض صفحة نجاح الدفع مع تفاصيل الحجز

### 5. التحقق اليدوي من حالة الدفع (اختياري)

يمكنك أيضاً التحقق يدوياً عن طريق:
- نسخ رابط callback ولصقه في حقل "Callback URL from Moyasar"
- الضغط على "Check Payment Status"

### 6. عرض تفاصيل الحجز

إذا كان الدفع ناجحاً (status=paid)، سيتم عرض:
- تفاصيل الحجز
- معلومات الدفع
- حالة المعاملة
- المبلغ المدفوع

## ملاحظات مهمة

- التطبيق يستخدم WebView لعرض صفحة الدفع داخل التطبيق نفسه
- يتم رصد نجاح الدفع تلقائياً عبر مراقبة URL redirects
- تأكد من أن `status=paid` موجود في رابط callback للتأكد من نجاح الدفع
- يجب أن يكون Reservation GUID صحيحاً للحصول على تفاصيل الحجز
- التطبيق يتعامل مع الأخطاء ويعرض رسائل واضحة للمستخدم
- يتطلب إذن الإنترنت على Android للوصول إلى Moyasar API

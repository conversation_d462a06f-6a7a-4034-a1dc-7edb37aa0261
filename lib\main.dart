import 'package:flutter/material.dart';
import 'screens/payment_screen.dart';
import 'screens/payment_success_screen.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Moyasar Payment App',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      initialRoute: '/',
      routes: {
        '/': (context) => const PaymentScreen(),
        '/success': (context) => const PaymentSuccessScreen(),
      },
    );
  }
}

class PaymentRequest {
  final String paymentMethod;
  final String reservationGuid;
  final String deviceToken;

  PaymentRequest({
    required this.paymentMethod,
    required this.reservationGuid,
    required this.deviceToken,
  });

  Map<String, dynamic> toJson() {
    return {
      'paymentMethod': paymentMethod,
      'reservationGuid': reservationGuid,
      'deviceToken': deviceToken,
    };
  }
}

class PaymentResponse {
  final int statusCode;
  final bool success;
  final String message;
  final String? data;

  PaymentResponse({
    required this.statusCode,
    required this.success,
    required this.message,
    this.data,
  });

  factory PaymentResponse.fromJson(Map<String, dynamic> json) {
    return PaymentResponse(
      statusCode: json['statusCode'] ?? 0,
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'],
    );
  }
}

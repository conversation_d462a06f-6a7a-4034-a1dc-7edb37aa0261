class PaymentRequest {
  final String paymentMethod;
  final String reservationGuid;
  final String deviceToken;

  PaymentRequest({
    required this.paymentMethod,
    required this.reservationGuid,
    required this.deviceToken,
  });

  Map<String, dynamic> toJson() {
    return {
      'paymentMethod': paymentMethod,
      'reservationGuid': reservationGuid,
      'deviceToken': deviceToken,
    };
  }
}

class PaymentResponse {
  final int statusCode;
  final bool success;
  final String message;
  final String? data;

  PaymentResponse({
    required this.statusCode,
    required this.success,
    required this.message,
    this.data,
  });

  factory PaymentResponse.fromJson(Map<String, dynamic> json) {
    return PaymentResponse(
      statusCode: json['statusCode'] ?? 0,
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'],
    );
  }
}

class ReservationResponse {
  final int statusCode;
  final bool success;
  final String message;
  final ReservationData? data;

  ReservationResponse({
    required this.statusCode,
    required this.success,
    required this.message,
    this.data,
  });

  factory ReservationResponse.fromJson(Map<String, dynamic> json) {
    return ReservationResponse(
      statusCode: json['statusCode'] ?? 0,
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null ? ReservationData.fromJson(json['data']) : null,
    );
  }
}

class ReservationData {
  final int id;
  final int userId;
  final String bookingId;
  final String reservationGUID;
  final String queuingOfficeId;
  final bool isRefunded;
  final bool isVoided;
  final bool isCaptured;
  final List<FlightInfo> flights;
  final List<TravelerInfo> travelers;
  final List<PaymentInfo> payments;

  ReservationData({
    required this.id,
    required this.userId,
    required this.bookingId,
    required this.reservationGUID,
    required this.queuingOfficeId,
    required this.isRefunded,
    required this.isVoided,
    required this.isCaptured,
    required this.flights,
    required this.travelers,
    required this.payments,
  });

  factory ReservationData.fromJson(Map<String, dynamic> json) {
    return ReservationData(
      id: json['id'] ?? 0,
      userId: json['userId'] ?? 0,
      bookingId: json['bookingId'] ?? '',
      reservationGUID: json['reservationGUID'] ?? '',
      queuingOfficeId: json['queuingOfficeId'] ?? '',
      isRefunded: json['isRefunded'] ?? false,
      isVoided: json['isVoided'] ?? false,
      isCaptured: json['isCaptured'] ?? false,
      flights: (json['flights'] as List<dynamic>?)
          ?.map((flight) => FlightInfo.fromJson(flight))
          .toList() ?? [],
      travelers: (json['travelers'] as List<dynamic>?)
          ?.map((traveler) => TravelerInfo.fromJson(traveler))
          .toList() ?? [],
      payments: (json['payments'] as List<dynamic>?)
          ?.map((payment) => PaymentInfo.fromJson(payment))
          .toList() ?? [],
    );
  }
}

class FlightInfo {
  final String flightOfferId;
  final String source;
  final String lastTicketingDate;
  final String reference;
  final String creationDate;
  final String currency;
  final double total;
  final double base;
  final double grandTotal;
  final double final_;
  final List<FlightDetail> flightDetails;

  FlightInfo({
    required this.flightOfferId,
    required this.source,
    required this.lastTicketingDate,
    required this.reference,
    required this.creationDate,
    required this.currency,
    required this.total,
    required this.base,
    required this.grandTotal,
    required this.final_,
    required this.flightDetails,
  });

  factory FlightInfo.fromJson(Map<String, dynamic> json) {
    return FlightInfo(
      flightOfferId: json['flightOfferId'] ?? '',
      source: json['source'] ?? '',
      lastTicketingDate: json['lastTicketingDate'] ?? '',
      reference: json['reference'] ?? '',
      creationDate: json['creationDate'] ?? '',
      currency: json['currency'] ?? '',
      total: (json['total'] ?? 0).toDouble(),
      base: (json['base'] ?? 0).toDouble(),
      grandTotal: (json['grandTotal'] ?? 0).toDouble(),
      final_: (json['final'] ?? 0).toDouble(),
      flightDetails: (json['flightDetails'] as List<dynamic>?)
          ?.map((detail) => FlightDetail.fromJson(detail))
          .toList() ?? [],
    );
  }
}

class FlightDetail {
  final int flightId;
  final String segmentId;
  final String departureIataCode;
  final String departureTerminal;
  final String departureAt;
  final String arrivalIataCode;
  final String arrivalAt;
  final String carrierCode;
  final String number;
  final String aircraftCode;
  final String duration;
  final int numberOfStops;

  FlightDetail({
    required this.flightId,
    required this.segmentId,
    required this.departureIataCode,
    required this.departureTerminal,
    required this.departureAt,
    required this.arrivalIataCode,
    required this.arrivalAt,
    required this.carrierCode,
    required this.number,
    required this.aircraftCode,
    required this.duration,
    required this.numberOfStops,
  });

  factory FlightDetail.fromJson(Map<String, dynamic> json) {
    return FlightDetail(
      flightId: json['flightId'] ?? 0,
      segmentId: json['segmentId'] ?? '',
      departureIataCode: json['departure_iataCode'] ?? '',
      departureTerminal: json['departure_terminal'] ?? '',
      departureAt: json['departure_at'] ?? '',
      arrivalIataCode: json['arrival_iataCode'] ?? '',
      arrivalAt: json['arrival_at'] ?? '',
      carrierCode: json['carrierCode'] ?? '',
      number: json['number'] ?? '',
      aircraftCode: json['aircraft_code'] ?? '',
      duration: json['duration'] ?? '',
      numberOfStops: json['numberOfStops'] ?? 0,
    );
  }
}

class TravelerInfo {
  final String travelerId;
  final String dateOfBirth;
  final String gender;
  final String firstName;
  final String lastName;
  final String documentNumber;
  final String documentExpiryDate;
  final String documentNationality;
  final String contactEmailAddress;
  final String contactPhoneNumber;

  TravelerInfo({
    required this.travelerId,
    required this.dateOfBirth,
    required this.gender,
    required this.firstName,
    required this.lastName,
    required this.documentNumber,
    required this.documentExpiryDate,
    required this.documentNationality,
    required this.contactEmailAddress,
    required this.contactPhoneNumber,
  });

  factory TravelerInfo.fromJson(Map<String, dynamic> json) {
    return TravelerInfo(
      travelerId: json['travelerId'] ?? '',
      dateOfBirth: json['dateOfBirth'] ?? '',
      gender: json['gender'] ?? '',
      firstName: json['firstName'] ?? '',
      lastName: json['lastName'] ?? '',
      documentNumber: json['document_number'] ?? '',
      documentExpiryDate: json['document_expiryDate'] ?? '',
      documentNationality: json['document_nationality'] ?? '',
      contactEmailAddress: json['contact_emailAddress'] ?? '',
      contactPhoneNumber: json['contact_phone_number'] ?? '',
    );
  }
}

class PaymentInfo {
  final String paymentId;
  final String status;
  final double amount;
  final double fee;
  final String currency;
  final String description;
  final String message;
  final String invoiceId;
  final String createdAt;
  final String updatedAt;
  final String company;
  final String name;
  final String number;

  PaymentInfo({
    required this.paymentId,
    required this.status,
    required this.amount,
    required this.fee,
    required this.currency,
    required this.description,
    required this.message,
    required this.invoiceId,
    required this.createdAt,
    required this.updatedAt,
    required this.company,
    required this.name,
    required this.number,
  });

  factory PaymentInfo.fromJson(Map<String, dynamic> json) {
    return PaymentInfo(
      paymentId: json['paymentId'] ?? '',
      status: json['status'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      fee: (json['fee'] ?? 0).toDouble(),
      currency: json['currency'] ?? '',
      description: json['description'] ?? '',
      message: json['message'] ?? '',
      invoiceId: json['invoiceId'] ?? '',
      createdAt: json['created_At'] ?? '',
      updatedAt: json['updated_At'] ?? '',
      company: json['company'] ?? '',
      name: json['name'] ?? '',
      number: json['number'] ?? '',
    );
  }
}

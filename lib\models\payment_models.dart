class PaymentRequest {
  final String paymentMethod;
  final String reservationGuid;
  final String deviceToken;

  PaymentRequest({
    required this.paymentMethod,
    required this.reservationGuid,
    required this.deviceToken,
  });

  Map<String, dynamic> toJson() {
    return {
      'paymentMethod': paymentMethod,
      'reservationGuid': reservationGuid,
      'deviceToken': deviceToken,
    };
  }
}

class PaymentResponse {
  final int statusCode;
  final bool success;
  final String message;
  final String? data;

  PaymentResponse({
    required this.statusCode,
    required this.success,
    required this.message,
    this.data,
  });

  factory PaymentResponse.fromJson(Map<String, dynamic> json) {
    return PaymentResponse(
      statusCode: json['statusCode'] ?? 0,
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'],
    );
  }
}

class ReservationResponse {
  final int statusCode;
  final bool success;
  final String message;
  final ReservationData? data;

  ReservationResponse({
    required this.statusCode,
    required this.success,
    required this.message,
    this.data,
  });

  factory ReservationResponse.fromJson(Map<String, dynamic> json) {
    return ReservationResponse(
      statusCode: json['statusCode'] ?? 0,
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null ? ReservationData.fromJson(json['data']) : null,
    );
  }
}

class ReservationData {
  final int id;
  final int userId;
  final String bookingId;
  final String reservationGUID;
  final bool isRefunded;
  final bool isVoided;
  final bool isCaptured;
  final List<PaymentInfo> payments;

  ReservationData({
    required this.id,
    required this.userId,
    required this.bookingId,
    required this.reservationGUID,
    required this.isRefunded,
    required this.isVoided,
    required this.isCaptured,
    required this.payments,
  });

  factory ReservationData.fromJson(Map<String, dynamic> json) {
    return ReservationData(
      id: json['id'] ?? 0,
      userId: json['userId'] ?? 0,
      bookingId: json['bookingId'] ?? '',
      reservationGUID: json['reservationGUID'] ?? '',
      isRefunded: json['isRefunded'] ?? false,
      isVoided: json['isVoided'] ?? false,
      isCaptured: json['isCaptured'] ?? false,
      payments: (json['payments'] as List<dynamic>?)
          ?.map((payment) => PaymentInfo.fromJson(payment))
          .toList() ?? [],
    );
  }
}

class PaymentInfo {
  final String paymentId;
  final String status;
  final double amount;
  final String currency;
  final String description;
  final String message;
  final String invoiceId;

  PaymentInfo({
    required this.paymentId,
    required this.status,
    required this.amount,
    required this.currency,
    required this.description,
    required this.message,
    required this.invoiceId,
  });

  factory PaymentInfo.fromJson(Map<String, dynamic> json) {
    return PaymentInfo(
      paymentId: json['paymentId'] ?? '',
      status: json['status'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      currency: json['currency'] ?? '',
      description: json['description'] ?? '',
      message: json['message'] ?? '',
      invoiceId: json['invoiceId'] ?? '',
    );
  }
}

import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../services/payment_service.dart';

class PaymentScreen extends StatefulWidget {
  const PaymentScreen({super.key});

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  final TextEditingController _reservationGuidController = TextEditingController();
  final TextEditingController _deviceTokenController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    // Set default values for testing
    _reservationGuidController.text = '2a89c8c1e4ef4b1f9aa19e2d22c24392';
    _deviceTokenController.text = 'string';
  }

  Future<void> _processPayment() async {
    if (_reservationGuidController.text.isEmpty || _deviceTokenController.text.isEmpty) {
      setState(() {
        _errorMessage = 'Please fill in all fields';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await PaymentService.createInvoice(
        reservationGuid: _reservationGuidController.text.trim(),
        deviceToken: _deviceTokenController.text.trim(),
      );

      if (response.success && response.data != null) {
        // Launch the payment URL
        final Uri paymentUrl = Uri.parse(response.data!);
        if (await canLaunchUrl(paymentUrl)) {
          await launchUrl(
            paymentUrl,
            mode: LaunchMode.externalApplication,
          );
          
          // Show success message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Payment page opened successfully!'),
                backgroundColor: Colors.green,
              ),
            );
          }
        } else {
          setState(() {
            _errorMessage = 'Could not launch payment URL';
          });
        }
      } else {
        setState(() {
          _errorMessage = response.message;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'An error occurred: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Moyasar Payment'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Payment Details',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            
            TextField(
              controller: _reservationGuidController,
              decoration: const InputDecoration(
                labelText: 'Reservation GUID',
                border: OutlineInputBorder(),
                hintText: 'Enter reservation GUID',
              ),
            ),
            const SizedBox(height: 16),
            
            TextField(
              controller: _deviceTokenController,
              decoration: const InputDecoration(
                labelText: 'Device Token',
                border: OutlineInputBorder(),
                hintText: 'Enter device token',
              ),
            ),
            const SizedBox(height: 24),
            
            if (_errorMessage != null)
              Container(
                padding: const EdgeInsets.all(12),
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  color: Colors.red.shade100,
                  border: Border.all(color: Colors.red),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _errorMessage!,
                  style: const TextStyle(color: Colors.red),
                ),
              ),
            
            ElevatedButton(
              onPressed: _isLoading ? null : _processPayment,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
              child: _isLoading
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        SizedBox(width: 12),
                        Text('Processing...'),
                      ],
                    )
                  : const Text(
                      'Proceed to Payment',
                      style: TextStyle(fontSize: 16),
                    ),
            ),
            
            const SizedBox(height: 24),
            const Text(
              'Note: After successful payment, you will be redirected to tak.runasp.net/moyasar',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _reservationGuidController.dispose();
    _deviceTokenController.dispose();
    super.dispose();
  }
}

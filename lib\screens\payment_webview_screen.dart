import 'dart:async';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../services/payment_service.dart';
import 'payment_callback_screen.dart';

class PaymentWebViewScreen extends StatefulWidget {
  final String paymentUrl;
  final String reservationGuid;

  const PaymentWebViewScreen({
    super.key,
    required this.paymentUrl,
    required this.reservationGuid,
  });

  @override
  State<PaymentWebViewScreen> createState() => _PaymentWebViewScreenState();
}

class _PaymentWebViewScreenState extends State<PaymentWebViewScreen> {
  late final WebViewController _controller;
  bool _isLoading = true;
  Timer? _urlCheckTimer;
  bool _hasNavigatedToCallback = false;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
    _startUrlMonitoring();
  }

  void _startUrlMonitoring() {
    // Check URL every 2 seconds
    _urlCheckTimer = Timer.periodic(const Duration(seconds: 2), (timer) async {
      if (!_hasNavigatedToCallback) {
        try {
          final currentUrl = await _controller.currentUrl();
          if (currentUrl != null) {
            print('Timer check - Current URL: $currentUrl');
            _checkForSuccessRedirect(currentUrl);
          }
        } catch (e) {
          print('Error getting current URL: $e');
        }
      }
    });
  }

  void _initializeWebView() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Update loading bar if needed
          },
          onPageStarted: (String url) {
            print('Page started: $url'); // للتتبع
            setState(() {
              _isLoading = true;
            });
            _checkForSuccessRedirect(url);
          },
          onPageFinished: (String url) {
            print('Page finished: $url'); // للتتبع
            setState(() {
              _isLoading = false;
            });
            _checkForSuccessRedirect(url);
          },
          onNavigationRequest: (NavigationRequest request) {
            print('Navigation request: ${request.url}'); // للتتبع
            _checkForSuccessRedirect(request.url);
            return NavigationDecision.navigate;
          },
          onUrlChange: (UrlChange change) {
            print('URL changed: ${change.url}'); // للتتبع
            if (change.url != null) {
              _checkForSuccessRedirect(change.url!);
            }
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.paymentUrl));
  }

  void _checkForSuccessRedirect(String url) {
    print('Checking URL: $url'); // للتتبع

    // Check if the URL contains tak.runasp.net/moyasar
    if (url.contains('tak.runasp.net/moyasar')) {
      print('Found tak.runasp.net/moyasar in URL'); // للتتبع

      try {
        // Parse URL parameters to check if payment was successful
        final urlParams = PaymentService.parseUrlParameters(url);
        final status = urlParams['status'];

        print('Payment status: $status'); // للتتبع
        print('All URL params: $urlParams'); // للتتبع

        if (status == 'paid') {
          print('Payment successful! Navigating to callback screen...'); // للتتبع

          // Payment successful, navigate to callback screen
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => PaymentCallbackScreen(
                callbackUrl: url,
                reservationGuid: widget.reservationGuid,
              ),
            ),
          );
        } else if (status != null) {
          // Payment failed or cancelled
          print('Payment failed with status: $status'); // للتتبع
          _showPaymentResult(false, 'Payment was not successful. Status: $status');
        }
      } catch (e) {
        print('Error parsing URL: $e'); // للتتبع
      }
    }
  }

  void _showPaymentResult(bool success, String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(success ? 'Payment Successful' : 'Payment Failed'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                Navigator.of(context).pop(); // Go back to payment screen
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ),
        ],
      ),
      body: Stack(
        children: [
          WebViewWidget(controller: _controller),
          if (_isLoading)
            const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Loading payment page...'),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
